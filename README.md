# Google Cloud Storage Redirect HTML Uploader

A Python script that creates redirect HTML files and uploads them to Google Cloud Storage via proxy.

## ✅ Quick Setup Checklist

### 🧰 1. Install Python Dependencies
```bash
# Create virtual environment (recommended)
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Mac/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 🔐 2. Set Up Google Cloud Authentication
1. **Go to Google Cloud Console** → IAM & Admin → Service Accounts
2. **Create a service account** with Storage Admin permissions
3. **Download the JSON credentials file**
4. **Place it in this folder** and name it `service_account.json`

### ⚙️ 3. Configure the Script
Edit `upload_redirect.py` and update these variables:
```python
BUCKET_NAME = "your-actual-bucket-name"  # Your GCS bucket name
CREDENTIALS_FILE = "service_account.json"  # Path to your JSON file
PROXY_URL = "http://127.0.0.1:8080"  # Your proxy URL
```

### 🚀 4. Run the Script
```bash
python upload_redirect.py
```

## 📋 What the Script Does

1. **📝 Asks for URL** - Prompts you to enter the destination URL
2. **📄 Generates HTML** - Creates a redirect HTML file with a random name
3. **🔐 Uses Proxy** - All API requests go through your specified proxy
4. **🌐 Uploads to GCS** - Uploads the file to your Google Cloud Storage bucket
5. **🔓 Makes Public** - Sets the file to be publicly accessible
6. **🔗 Returns URL** - Gives you the public URL anyone can access

## 🎯 Example Usage

```
🚀 Google Cloud Storage Redirect HTML Uploader
============================================================

📝 Step 1: Enter the URL to redirect to
URL: google.com
✅ Will redirect to: https://google.com

📄 Step 2: Generating redirect HTML file...
✅ Generated file: redirect-a7b3c9d2.html

🔐 Step 3: Setting up Google Cloud client (via proxy)...
✅ Client setup complete

🌐 Step 4: Uploading to Google Cloud Storage...
📤 Uploading redirect-a7b3c9d2.html to bucket 'my-bucket'...
🔓 Making file public...

============================================================
🎉 SUCCESS!
============================================================
📁 File: redirect-a7b3c9d2.html
🔗 Public URL: https://storage.googleapis.com/my-bucket/redirect-a7b3c9d2.html
➡️  Redirects to: https://google.com

✅ Anyone with the URL can now access your redirect!
============================================================
```

## 🔧 Features

- ✅ **Proxy Support** - All requests go through your proxy
- ✅ **Random Filenames** - Generates unique names like `redirect-a7b3c9d2.html`
- ✅ **Public Access** - Files are automatically made public
- ✅ **URL Validation** - Automatically adds `https://` if missing
- ✅ **Error Handling** - Clear error messages for common issues
- ✅ **Beautiful HTML** - Creates nice-looking redirect pages with loading spinner

## 🛠️ Troubleshooting

**Error: Credentials file not found**
- Make sure `service_account.json` is in the same folder as the script

**Error: Bucket not found**
- Check that `BUCKET_NAME` matches your actual bucket name
- Ensure your service account has access to the bucket

**Error: Proxy connection failed**
- Verify your proxy URL is correct
- Make sure your proxy allows connections to Google APIs

## 📁 File Structure
```
your-project/
├── upload_redirect.py      # Main script
├── requirements.txt        # Python dependencies
├── service_account.json    # Your GCS credentials (you need to add this)
└── README.md              # This file
```
