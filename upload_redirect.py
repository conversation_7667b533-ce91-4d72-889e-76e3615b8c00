#!/usr/bin/env python3
"""
Google Cloud Storage Redirect HTML Uploader (with Proxy Support)
================================================================

This script:
1. Asks user for a URL to redirect to
2. Generates a redirect HTML file with that URL
3. Uploads it to your Google Cloud Storage bucket via proxy
4. Makes it public so anyone can access it
5. Returns the public URL

Requirements:
- Google Cloud Storage bucket
- Service account JSON credentials
- Proxy server (for API requests)
"""

import random
import string
import os
import sys
from google.cloud import storage
from google.oauth2 import service_account
import requests

# ===== CONFIGURATION =====
# TODO: Update these values for your setup
BUCKET_NAME = "earlahdelir"  # Replace with your actual bucket name
CREDENTIALS_FILE = "service_account.json"  # Path to your service account JSON file
PROXY_URL = "http://a9a69fc28d6b89fa4801__cr.ua:<EMAIL>:823"  # Replace with your proxy URL

def parse_proxy_url(proxy_url):
    """Parse proxy URL to extract authentication if present"""
    from urllib.parse import urlparse
    parsed = urlparse(proxy_url)

    if parsed.username and parsed.password:
        # Proxy URL contains authentication - return full URL for requests
        return proxy_url, parsed.username, parsed.password
    else:
        # No authentication in URL
        return proxy_url, None, None

def validate_url(url):
    """Basic URL validation"""
    if not url.startswith(('http://', 'https://')):
        return f"https://{url}"  # Add https if missing
    return url

def generate_random_filename():
    """Generate a random filename for the HTML file"""
    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    return f"{random_suffix}.html"

def create_redirect_html(redirect_url):
    """Create HTML content that redirects to the specified URL"""
    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="refresh" content="0; url={redirect_url}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting...</title>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>Redirecting...</h2>
        <p>If you are not redirected automatically, <a href="{redirect_url}">click here</a>.</p>
    </div>
    <script>
        setTimeout(function() {{
            window.location.href = "{redirect_url}";
        }}, 1000);
    </script>
</body>
</html>"""
    return html_content

def setup_proxy_client():
    """Set up Google Cloud Storage client with proxy support"""
    try:
        # Check if credentials file exists
        if not os.path.exists(CREDENTIALS_FILE):
            print(f"Error: Credentials file '{CREDENTIALS_FILE}' not found!")
            print("Please download your service account JSON file and place it in this directory.")
            sys.exit(1)

        # Load credentials with proper scopes for Google Cloud Storage
        credentials = service_account.Credentials.from_service_account_file(
            CREDENTIALS_FILE,
            scopes=['https://www.googleapis.com/auth/cloud-platform']
        )

        # Parse proxy URL and extract authentication
        proxy_url, proxy_user, proxy_pass = parse_proxy_url(PROXY_URL)

        # Create a custom HTTP adapter that works with Google Cloud Storage
        from google.auth.transport.requests import Request

        # Create session with proxy authentication
        session = requests.Session()
        session.proxies = {
            'http': proxy_url,
            'https': proxy_url
        }

        # Set up proxy authentication if credentials are provided
        if proxy_user and proxy_pass:
            from requests.auth import HTTPProxyAuth
            session.auth = HTTPProxyAuth(proxy_user, proxy_pass)

        # Create custom request adapter for credential refresh
        request_adapter = Request(session)

        # Refresh credentials using proxy
        credentials.refresh(request_adapter)

        # Create a wrapper class that provides the attributes Google Cloud expects
        # and ensures credentials are applied to all requests
        class AuthenticatedProxySession:
            def __init__(self, session, credentials):
                self._session = session
                self._credentials = credentials
                self.is_mtls = False  # Required attribute for Google Cloud client

            def request(self, method, url, **kwargs):
                # Apply credentials to the request
                self._credentials.apply(kwargs.setdefault('headers', {}))

                # Refresh credentials if they're expired
                if self._credentials.expired:
                    request_adapter = Request(self._session)
                    self._credentials.refresh(request_adapter)
                    # Re-apply refreshed credentials
                    self._credentials.apply(kwargs['headers'])

                return self._session.request(method, url, **kwargs)

            def __getattr__(self, name):
                # Delegate all other attributes to the underlying session
                return getattr(self._session, name)

        # Create authenticated session wrapper
        auth_session = AuthenticatedProxySession(session, credentials)

        # Create client with the authenticated proxy session
        client = storage.Client(credentials=credentials, _http=auth_session)

        return client

    except Exception as e:
        print(f"Error setting up Google Cloud client: {e}")
        sys.exit(1)

def upload_and_make_public(client, filename, html_content):
    """Upload HTML file to bucket and make it public"""
    try:
        # Get bucket
        bucket = client.bucket(BUCKET_NAME)
        
        # Create blob (file object in bucket)
        blob = bucket.blob(filename)
        
        # Upload HTML content
        print(f"Uploading {filename} to bucket '{BUCKET_NAME}'...")
        blob.upload_from_string(html_content, content_type='text/html')
        
        # Make the file public
        print("Making file public...")
        blob.make_public()
        
        # Generate public URL
        public_url = f"https://storage.googleapis.com/{BUCKET_NAME}/{filename}"
        
        return public_url
        
    except Exception as e:
        print(f"Error uploading file: {e}")
        sys.exit(1)

def main():
    """Main function"""
    print("=" * 60)
    print("Google Cloud Storage Redirect HTML Uploader")
    print("=" * 60)
    print()
    
    # Step 1: Ask user for URL
    print("Step 1: Enter the URL to redirect to")
    while True:
        redirect_url = input("URL: ").strip()
        if redirect_url:
            redirect_url = validate_url(redirect_url)
            print(f"Will redirect to: {redirect_url}")
            break
        else:
            print("Please enter a valid URL!")
    
    print()
    
    # Step 2: Generate filename
    print("Step 2: Generating redirect HTML file...")
    filename = generate_random_filename()
    html_content = create_redirect_html(redirect_url)
    print(f"Generated file: {filename}")
    
    print()
    
    # Step 3: Setup client with proxy
    print("Step 3: Setting up Google Cloud client (via proxy)...")
    client = setup_proxy_client()
    print("Client setup complete")
    
    print()
    
    # Step 4: Upload and make public
    print("Step 4: Uploading to Google Cloud Storage...")
    public_url = upload_and_make_public(client, filename, html_content)
    
    print()
    print("=" * 60)
    print("SUCCESS!")
    print("=" * 60)
    print(f"File: {filename}")
    print(f"Public URL: {public_url}")
    print(f"Redirects to: {redirect_url}")
    print()
    print("Anyone with the URL can now access your redirect!")
    print("=" * 60)

if __name__ == "__main__":
    main()
