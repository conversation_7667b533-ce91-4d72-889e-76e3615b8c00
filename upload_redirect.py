import random
import string
import os
import sys
from google.cloud import storage
from google.oauth2 import service_account
import requests


BUCKET_NAME = "earlahdelir"
CREDENTIALS_FILE = "service_account.json"
PROXY_URL = "http://a9a69fc28d6b89fa4801__cr.ua:<EMAIL>:823"

def parse_proxy_url(proxy_url):
    """Parse proxy URL to extract authentication if present"""
    from urllib.parse import urlparse
    parsed = urlparse(proxy_url)

    if parsed.username and parsed.password:
        return proxy_url, parsed.username, parsed.password
    else:
        return proxy_url, None, None

def validate_url(url):
    if not url.startswith(('http://', 'https://')):
        return f"https://{url}"
    return url

def generate_random_filename():
    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    return f"{random_suffix}.html"

def create_redirect_html(redirect_url):
    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="refresh" content="0; url={redirect_url}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting...</title>
</head>
<body>
    <script>
        setTimeout(function() {{
            window.location.href = "{redirect_url}";
        }}, 10);
    </script>
</body>
</html>"""
    return html_content

def setup_proxy_client():
    try:
        if not os.path.exists(CREDENTIALS_FILE):
            print(f"Error: Credentials file '{CREDENTIALS_FILE}' not found!")
            print("Please download your service account JSON file and place it in this directory.")
            sys.exit(1)

        credentials = service_account.Credentials.from_service_account_file(
            CREDENTIALS_FILE,
            scopes=['https://www.googleapis.com/auth/cloud-platform']
        )

        proxy_url, proxy_user, proxy_pass = parse_proxy_url(PROXY_URL)

        from google.auth.transport.requests import Request

        session = requests.Session()
        session.proxies = {
            'http': proxy_url,
            'https': proxy_url
        }

        if proxy_user and proxy_pass:
            from requests.auth import HTTPProxyAuth
            session.auth = HTTPProxyAuth(proxy_user, proxy_pass)

        request_adapter = Request(session)

        credentials.refresh(request_adapter)

        class AuthenticatedProxySession:
            def __init__(self, session, credentials):
                self._session = session
                self._credentials = credentials
                self.is_mtls = False

            def request(self, method, url, **kwargs):
                self._credentials.apply(kwargs.setdefault('headers', {}))
                if self._credentials.expired:
                    request_adapter = Request(self._session)
                    self._credentials.refresh(request_adapter)
                    self._credentials.apply(kwargs['headers'])

                return self._session.request(method, url, **kwargs)

            def __getattr__(self, name):
                return getattr(self._session, name)

        auth_session = AuthenticatedProxySession(session, credentials)

        client = storage.Client(credentials=credentials, _http=auth_session)

        return client

    except Exception as e:
        print(f"Error setting up Google Cloud client: {e}")
        sys.exit(1)

def upload_and_make_public(client, filename, html_content):
    try:
        bucket = client.bucket(BUCKET_NAME)
        
        blob = bucket.blob(filename)
        
        print(f"Uploading {filename} to bucket '{BUCKET_NAME}'...")
        blob.upload_from_string(html_content, content_type='text/html')
        
        print("Making file public...")
        try:
            blob.make_public()
        except Exception as e:
            if "uniform bucket-level access" in str(e).lower():
                print("Note: Bucket has uniform bucket-level access enabled.")
                print("File is already publicly accessible if bucket policy allows it.")
                print("To make individual files public, you need to disable uniform bucket-level access")
                print("or configure bucket-level IAM policies.")
            else:
                raise e
        
        public_url = f"https://storage.googleapis.com/{BUCKET_NAME}/{filename}"
        
        return public_url
        
    except Exception as e:
        print(f"Error uploading file: {e}")
        sys.exit(1)

def main():
    print("=" * 60)
    print("Google Cloud Storage Redirect HTML Uploader")
    print("=" * 60)
    print()
    
    print("Step 1: Enter the URL to redirect to")
    while True:
        redirect_url = input("URL: ").strip()
        if redirect_url:
            redirect_url = validate_url(redirect_url)
            print(f"Will redirect to: {redirect_url}")
            break
        else:
            print("Please enter a valid URL!")
    
    print()
    
    print("Step 2: Generating redirect HTML file...")
    filename = generate_random_filename()
    html_content = create_redirect_html(redirect_url)
    print(f"Generated file: {filename}")
    
    print()
    
    print("Step 3: Setting up Google Cloud client (via proxy)...")
    client = setup_proxy_client()
    print("Client setup complete")
    
    print()
    
    print("Step 4: Uploading to Google Cloud Storage...")
    public_url = upload_and_make_public(client, filename, html_content)
    
    print()
    print("=" * 60)
    print("SUCCESS!")
    print("=" * 60)
    print(f"File: {filename}")
    print(f"Public URL: {public_url}")
    print(f"Redirects to: {redirect_url}")
    print()
    print("Anyone with the URL can now access your redirect!")
    print("=" * 60)

if __name__ == "__main__":
    main()
